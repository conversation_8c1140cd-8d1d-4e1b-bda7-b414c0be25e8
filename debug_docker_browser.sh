#!/bin/bash
# Debug script to check browser service status in Docker

echo "=== Docker Browser Service Debug ==="
echo "Date: $(date)"
echo

# Check if we're in Docker
if [ -f /.dockerenv ]; then
    echo "✅ Running inside Docker container"
else
    echo "❌ Not running in Docker - this script is for Docker debugging"
    exit 1
fi

echo
echo "=== Settings Check ==="
SETTINGS_FILE="/a0/tmp/settings.json"
if [ -f "$SETTINGS_FILE" ]; then
    echo "✅ Settings file exists: $SETTINGS_FILE"
    INTERACTIVE_MODE=$(python3 -c "
import json
try:
    with open('$SETTINGS_FILE', 'r') as f:
        settings = json.load(f)
    print(settings.get('browser_interactive_mode', False))
except Exception as e:
    print('Error:', e)
" 2>&1)
    echo "Interactive mode setting: $INTERACTIVE_MODE"
else
    echo "❌ Settings file not found: $SETTINGS_FILE"
fi

echo
echo "=== Process Check ==="
echo "Checking for browser service processes..."

# Check Xvfb
if pgrep -x "Xvfb" > /dev/null; then
    echo "✅ Xvfb is running (PID: $(pgrep -x Xvfb))"
else
    echo "❌ Xvfb is not running"
fi

# Check fluxbox
if pgrep -x "fluxbox" > /dev/null; then
    echo "✅ Fluxbox is running (PID: $(pgrep -x fluxbox))"
else
    echo "❌ Fluxbox is not running"
fi

# Check x11vnc
if pgrep -x "x11vnc" > /dev/null; then
    echo "✅ x11vnc is running (PID: $(pgrep -x x11vnc))"
else
    echo "❌ x11vnc is not running"
fi

# Check websockify
if pgrep -f "websockify" > /dev/null; then
    echo "✅ websockify is running (PID: $(pgrep -f websockify))"
else
    echo "❌ websockify is not running"
fi

# Check Chrome/Chromium
if pgrep -f "chrome\|chromium" > /dev/null; then
    echo "✅ Chrome/Chromium is running (PID: $(pgrep -f 'chrome\|chromium'))"
else
    echo "❌ Chrome/Chromium is not running"
fi

echo
echo "=== Port Check ==="
echo "Checking if ports are listening..."

for port in 5900 6080 9222; do
    if netstat -ln 2>/dev/null | grep -q ":$port "; then
        echo "✅ Port $port is listening"
    else
        echo "❌ Port $port is not listening"
    fi
done

echo
echo "=== Supervisor Status ==="
if command -v supervisorctl > /dev/null; then
    echo "Browser service supervisor status:"
    supervisorctl status run_browser_service 2>/dev/null || echo "❌ Could not get supervisor status"
else
    echo "❌ supervisorctl not available"
fi

echo
echo "=== Package Check ==="
echo "Checking if required packages are installed..."

for pkg in xvfb x11vnc websockify fluxbox chromium; do
    if command -v $pkg > /dev/null 2>&1; then
        echo "✅ $pkg is installed"
    else
        echo "❌ $pkg is not installed"
    fi
done

echo
echo "=== noVNC Files Check ==="
if [ -d "/usr/share/novnc" ]; then
    echo "✅ /usr/share/novnc directory exists"
    echo "Contents:"
    ls -la /usr/share/novnc/ 2>/dev/null || echo "❌ Could not list contents"
else
    echo "❌ /usr/share/novnc directory does not exist"
fi

echo
echo "=== Manual Start Test ==="
echo "Attempting to manually start browser service components..."

# Set environment
export DISPLAY=:99
export HOME=/tmp
export USER=root

echo "Starting Xvfb..."
if ! pgrep -x "Xvfb" > /dev/null; then
    Xvfb :99 -screen 0 1920x1080x24 -ac +extension GLX +render -noreset &
    sleep 2
    if pgrep -x "Xvfb" > /dev/null; then
        echo "✅ Xvfb started successfully"
    else
        echo "❌ Failed to start Xvfb"
    fi
else
    echo "✅ Xvfb already running"
fi

echo "Starting fluxbox..."
if ! pgrep -x "fluxbox" > /dev/null; then
    fluxbox -display :99 &
    sleep 1
    if pgrep -x "fluxbox" > /dev/null; then
        echo "✅ Fluxbox started successfully"
    else
        echo "❌ Failed to start fluxbox"
    fi
else
    echo "✅ Fluxbox already running"
fi

echo "Starting x11vnc..."
if ! pgrep -x "x11vnc" > /dev/null; then
    x11vnc -display :99 -nopw -forever -shared -rfbport 5900 -bg
    sleep 1
    if pgrep -x "x11vnc" > /dev/null; then
        echo "✅ x11vnc started successfully"
    else
        echo "❌ Failed to start x11vnc"
    fi
else
    echo "✅ x11vnc already running"
fi

echo "Starting websockify..."
if ! pgrep -f "websockify.*6080" > /dev/null; then
    websockify --web /usr/share/novnc 6080 localhost:5900 &
    sleep 2
    if pgrep -f "websockify.*6080" > /dev/null; then
        echo "✅ websockify started successfully"
    else
        echo "❌ Failed to start websockify"
    fi
else
    echo "✅ websockify already running"
fi

echo "Starting Chrome..."
if ! pgrep -f "chrome.*remote-debugging-port=9222" > /dev/null; then
    # Find Chrome binary
    CHROME_BIN=""
    for chrome in chromium chromium-browser google-chrome google-chrome-stable; do
        if command -v $chrome &> /dev/null; then
            CHROME_BIN=$chrome
            break
        fi
    done
    
    if [ -n "$CHROME_BIN" ]; then
        echo "Using Chrome binary: $CHROME_BIN"
        $CHROME_BIN \
            --remote-debugging-port=9222 \
            --remote-debugging-address=0.0.0.0 \
            --user-data-dir=/tmp/browser_profile_manual \
            --no-first-run \
            --no-default-browser-check \
            --disable-default-apps \
            --disable-dev-shm-usage \
            --disable-gpu \
            --no-sandbox \
            --disable-setuid-sandbox \
            --disable-background-timer-throttling \
            --disable-backgrounding-occluded-windows \
            --disable-renderer-backgrounding \
            --window-size=1920,1080 \
            --window-position=0,0 \
            --enable-logging \
            --log-level=0 \
            about:blank &
        sleep 3
        if pgrep -f "chrome.*remote-debugging-port=9222" > /dev/null; then
            echo "✅ Chrome started successfully"
        else
            echo "❌ Failed to start Chrome"
        fi
    else
        echo "❌ No Chrome binary found"
    fi
else
    echo "✅ Chrome already running"
fi

echo
echo "=== Final Status ==="
echo "VNC Server: vnc://localhost:5900"
echo "noVNC Web: http://localhost:6080/vnc.html"
echo "Chrome DevTools: http://localhost:9222"
echo
echo "=== Test URLs ==="
echo "Try these URLs from the host machine:"
echo "- VNC Web Client: http://localhost:50080/browser_vnc"
echo "- noVNC Direct: http://localhost:56080/vnc.html"
echo "- Chrome DevTools: http://localhost:59222"
echo
echo "Debug complete!"
