#!/bin/bash
set -e

# install playwright - moved to install A0
# bash /ins/install_playwright.sh "$@"

# searxng - moved to base image
# bash /ins/install_searxng.sh "$@"

# Install browser service dependencies for manual control
echo "Installing browser service dependencies..."
apt-get update && apt-get install -y \
    xvfb \
    x11vnc \
    websockify \
    fluxbox \
    chromium \
    && rm -rf /var/lib/apt/lists/*

# Install actual noVNC client
echo "Installing noVNC web client..."
apt-get install -y novnc

# If novnc package doesn't provide the files, download them manually
if [ ! -f "/usr/share/novnc/vnc.html" ] && [ ! -f "/usr/share/novnc/vnc_lite.html" ]; then
    echo "Downloading noVNC client manually..."
    cd /tmp
    wget -q https://github.com/novnc/noVNC/archive/v1.4.0.tar.gz
    tar -xzf v1.4.0.tar.gz
    mkdir -p /usr/share/novnc
    cp -r noVNC-1.4.0/* /usr/share/novnc/
    rm -rf noVNC-1.4.0 v1.4.0.tar.gz
fi

# Ensure we have the main noVNC files
if [ ! -f "/usr/share/novnc/vnc.html" ] && [ -f "/usr/share/novnc/vnc_lite.html" ]; then
    # Create a redirect from vnc.html to vnc_lite.html
    echo '<!DOCTYPE html><html><head><meta http-equiv="refresh" content="0; url=vnc_lite.html"></head><body>Redirecting to noVNC...</body></html>' > /usr/share/novnc/vnc.html
elif [ ! -f "/usr/share/novnc/vnc.html" ] && [ ! -f "/usr/share/novnc/vnc_lite.html" ]; then
    # Fallback: create a basic redirect to Agent Zero's VNC client
    echo '<!DOCTYPE html><html><head><title>noVNC</title></head><body><h1>Agent Zero VNC Client</h1><p>VNC web interface is built into Agent Zero.</p></body></html>' > /usr/share/novnc/vnc.html
fi

echo "Browser service dependencies installed successfully."