#!/bin/bash
# Fix script to get browser service working in Docker

echo "=== Docker Browser Service Fix ==="
echo "This script will install and start the browser service components"
echo

# Check if we're in Docker
if [ ! -f /.dockerenv ]; then
    echo "❌ This script is designed to run inside a Docker container"
    exit 1
fi

echo "✅ Running inside Docker container"

# Update package list
echo "Updating package list..."
apt-get update -qq

# Install required packages
echo "Installing browser service dependencies..."
apt-get install -y \
    xvfb \
    x11vnc \
    websockify \
    fluxbox \
    chromium \
    novnc \
    wget \
    curl \
    netstat-nat

# Install noVNC if not present
echo "Setting up noVNC..."
mkdir -p /usr/share/novnc

# Download noVNC if the directory is empty or doesn't have the main files
if [ ! -f "/usr/share/novnc/vnc.html" ] && [ ! -f "/usr/share/novnc/vnc_lite.html" ]; then
    echo "Downloading noVNC client..."
    cd /tmp
    wget -q https://github.com/novnc/noVNC/archive/v1.4.0.tar.gz
    tar -xzf v1.4.0.tar.gz
    cp -r noVNC-1.4.0/* /usr/share/novnc/
    rm -rf noVNC-1.4.0 v1.4.0.tar.gz
    echo "✅ noVNC installed"
else
    echo "✅ noVNC already present"
fi

# Ensure we have a working noVNC setup
if [ ! -f "/usr/share/novnc/vnc.html" ] && [ -f "/usr/share/novnc/vnc_lite.html" ]; then
    echo "Creating vnc.html redirect..."
    echo '<!DOCTYPE html><html><head><meta http-equiv="refresh" content="0; url=vnc_lite.html"></head><body>Redirecting to noVNC...</body></html>' > /usr/share/novnc/vnc.html
fi

# Set up environment
export DISPLAY=:99
export HOME=/tmp
export USER=root

echo "Setting up browser service environment..."

# Kill any existing processes
echo "Stopping any existing browser service processes..."
pkill -f "websockify.*6080" 2>/dev/null || true
pkill -f "chrome.*remote-debugging-port=9222" 2>/dev/null || true
pkill -x "x11vnc" 2>/dev/null || true
pkill -x "fluxbox" 2>/dev/null || true
pkill -x "Xvfb" 2>/dev/null || true

sleep 2

# Start Xvfb
echo "Starting Xvfb..."
Xvfb :99 -screen 0 1920x1080x24 -ac +extension GLX +render -noreset &
sleep 2

if pgrep -x "Xvfb" > /dev/null; then
    echo "✅ Xvfb started successfully"
else
    echo "❌ Failed to start Xvfb"
    exit 1
fi

# Start fluxbox
echo "Starting fluxbox window manager..."
fluxbox -display :99 &
sleep 1

if pgrep -x "fluxbox" > /dev/null; then
    echo "✅ Fluxbox started successfully"
else
    echo "❌ Failed to start fluxbox"
fi

# Start x11vnc
echo "Starting VNC server..."
x11vnc -display :99 -nopw -forever -shared -rfbport 5900 -bg
sleep 1

if pgrep -x "x11vnc" > /dev/null; then
    echo "✅ VNC server started successfully"
else
    echo "❌ Failed to start VNC server"
    exit 1
fi

# Start websockify
echo "Starting websockify (noVNC web server)..."
websockify --web /usr/share/novnc 6080 localhost:5900 &
sleep 2

if pgrep -f "websockify.*6080" > /dev/null; then
    echo "✅ websockify started successfully"
else
    echo "❌ Failed to start websockify"
    exit 1
fi

# Start Chrome
echo "Starting Chrome with remote debugging..."

# Find Chrome binary
CHROME_BIN=""
for chrome in chromium chromium-browser google-chrome google-chrome-stable; do
    if command -v $chrome &> /dev/null; then
        CHROME_BIN=$chrome
        break
    fi
done

if [ -z "$CHROME_BIN" ]; then
    echo "❌ No Chrome binary found!"
    exit 1
fi

echo "Using Chrome binary: $CHROME_BIN"

# Create user data directory
mkdir -p /tmp/browser_profile_manual

$CHROME_BIN \
    --remote-debugging-port=9222 \
    --remote-debugging-address=0.0.0.0 \
    --user-data-dir=/tmp/browser_profile_manual \
    --no-first-run \
    --no-default-browser-check \
    --disable-default-apps \
    --disable-dev-shm-usage \
    --disable-gpu \
    --no-sandbox \
    --disable-setuid-sandbox \
    --disable-background-timer-throttling \
    --disable-backgrounding-occluded-windows \
    --disable-renderer-backgrounding \
    --disable-web-security \
    --disable-features=TranslateUI \
    --disable-ipc-flooding-protection \
    --window-size=1920,1080 \
    --window-position=0,0 \
    --enable-logging \
    --log-level=0 \
    about:blank &

sleep 3

if pgrep -f "chrome.*remote-debugging-port=9222" > /dev/null; then
    echo "✅ Chrome started successfully"
else
    echo "❌ Failed to start Chrome"
    exit 1
fi

# Verify all services are running
echo
echo "=== Service Status ==="
echo "Xvfb: $(pgrep -x Xvfb > /dev/null && echo '✅ Running' || echo '❌ Not running')"
echo "Fluxbox: $(pgrep -x fluxbox > /dev/null && echo '✅ Running' || echo '❌ Not running')"
echo "VNC Server: $(pgrep -x x11vnc > /dev/null && echo '✅ Running' || echo '❌ Not running')"
echo "websockify: $(pgrep -f 'websockify.*6080' > /dev/null && echo '✅ Running' || echo '❌ Not running')"
echo "Chrome: $(pgrep -f 'chrome.*remote-debugging-port=9222' > /dev/null && echo '✅ Running' || echo '❌ Not running')"

echo
echo "=== Port Status ==="
for port in 5900 6080 9222; do
    if netstat -ln 2>/dev/null | grep -q ":$port "; then
        echo "Port $port: ✅ Listening"
    else
        echo "Port $port: ❌ Not listening"
    fi
done

echo
echo "=== Connection Information ==="
echo "VNC Server: vnc://localhost:5900"
echo "noVNC Web: http://localhost:6080/vnc.html"
echo "Chrome DevTools: http://localhost:9222"
echo
echo "=== From Host Machine ==="
echo "If Docker ports are mapped correctly, try:"
echo "- Agent Zero VNC: http://localhost:50080/browser_vnc"
echo "- noVNC Direct: http://localhost:56080/vnc.html"
echo "- Chrome DevTools: http://localhost:59222"
echo
echo "=== Enable Interactive Mode ==="
echo "Make sure to enable 'Interactive Mode' in Agent Zero settings:"
echo "1. Go to Settings > Browser Model"
echo "2. Enable 'Enable Interactive Mode'"
echo "3. Save settings"

# Update settings to enable interactive mode
SETTINGS_FILE="/a0/work_dir/settings.json"
if [ -f "$SETTINGS_FILE" ]; then
    echo
    echo "Updating settings to enable interactive mode..."
    python3 -c "
import json
try:
    with open('$SETTINGS_FILE', 'r') as f:
        settings = json.load(f)
    settings['browser_interactive_mode'] = True
    with open('$SETTINGS_FILE', 'w') as f:
        json.dump(settings, f, indent=2)
    print('✅ Settings updated - interactive mode enabled')
except Exception as e:
    print('❌ Failed to update settings:', e)
"
else
    echo "Settings file not found at $SETTINGS_FILE"
fi

echo
echo "✅ Browser service setup complete!"
echo "The browser agent and user will now share the same browser instance."
