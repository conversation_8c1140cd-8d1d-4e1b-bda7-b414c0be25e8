<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Service Test - Agent Zero</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #444;
        }
        
        .test-section h2 {
            margin-top: 0;
            color: #4a90e2;
        }
        
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            font-weight: bold;
            display: inline-block;
            margin: 5px 0;
        }
        
        .status.success {
            background: #27ae60;
            color: white;
        }
        
        .status.error {
            background: #e74c3c;
            color: white;
        }
        
        .status.warning {
            background: #f39c12;
            color: black;
        }
        
        .btn {
            background: #4a90e2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #357abd;
        }
        
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        pre {
            background: #1a1a1a;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            border: 1px solid #444;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
        }
        
        .vnc-test {
            border: 2px solid #444;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .vnc-test iframe {
            width: 100%;
            height: 400px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖥️ Browser Service Test</h1>
        <p>This page tests the browser service components and VNC connectivity.</p>
        
        <div class="test-section">
            <h2>Service Status</h2>
            <button class="btn" onclick="testBrowserService()">Test Browser Service</button>
            <button class="btn" onclick="startBrowserService()">Start Browser Service</button>
            <div id="service-status"></div>
        </div>
        
        <div class="test-grid">
            <div class="test-section">
                <h2>VNC Connection Test</h2>
                <button class="btn" onclick="testVNCConnection()">Test VNC Paths</button>
                <div id="vnc-status"></div>
            </div>
            
            <div class="test-section">
                <h2>API Endpoints Test</h2>
                <button class="btn" onclick="testAPIEndpoints()">Test API Endpoints</button>
                <div id="api-status"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>Live VNC Test</h2>
            <p>This will attempt to load the VNC client directly:</p>
            <div class="vnc-test">
                <iframe id="vnc-test-frame" src="about:blank"></iframe>
            </div>
            <button class="btn" onclick="loadVNCClient()">Load VNC Client</button>
            <button class="btn" onclick="loadAgentZeroVNC()">Load Agent Zero VNC</button>
        </div>
        
        <div class="test-section">
            <h2>Raw Test Results</h2>
            <pre id="raw-results">Click "Test Browser Service" to see detailed results...</pre>
        </div>
    </div>

    <script>
        async function testBrowserService() {
            const statusDiv = document.getElementById('service-status');
            const rawResults = document.getElementById('raw-results');
            
            statusDiv.innerHTML = '<div class="status warning">Testing...</div>';
            
            try {
                const response = await fetch('/browser_test', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });
                
                const data = await response.json();
                rawResults.textContent = JSON.stringify(data, null, 2);
                
                if (data.status === 'success') {
                    const overallStatus = data.overall_status === 'healthy' ? 'success' : 'warning';
                    statusDiv.innerHTML = `
                        <div class="status ${overallStatus}">Overall Status: ${data.overall_status}</div>
                        <p><strong>Processes:</strong> ${data.summary.all_processes_running ? '✅' : '❌'}</p>
                        <p><strong>Ports:</strong> ${data.summary.all_ports_listening ? '✅' : '❌'}</p>
                        <p><strong>Service:</strong> ${data.summary.browser_service_healthy ? '✅' : '❌'}</p>
                    `;
                } else {
                    statusDiv.innerHTML = `<div class="status error">Error: ${data.message}</div>`;
                }
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">Request failed: ${error.message}</div>`;
                rawResults.textContent = `Error: ${error.message}`;
            }
        }
        
        async function startBrowserService() {
            try {
                const response = await fetch('/browser_service_control', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ action: 'start' })
                });
                
                const data = await response.json();
                alert(`Service start result: ${data.status} - ${data.message}`);
            } catch (error) {
                alert(`Failed to start service: ${error.message}`);
            }
        }
        
        async function testVNCConnection() {
            const statusDiv = document.getElementById('vnc-status');
            statusDiv.innerHTML = '<div class="status warning">Testing VNC paths...</div>';
            
            const paths = [
                'http://localhost:6080/vnc.html?host=localhost&port=6080',
                'http://localhost:6080/vnc_lite.html?host=localhost&port=6080',
                'http://localhost:6080/vnc.html',
                'http://localhost:6080/',
                '/browser_vnc'
            ];
            
            let results = [];
            
            for (const path of paths) {
                try {
                    const response = await fetch(path, { method: 'HEAD' });
                    results.push(`${path}: ${response.ok ? '✅' : '❌'} (${response.status})`);
                } catch (error) {
                    results.push(`${path}: ❌ (${error.message})`);
                }
            }
            
            statusDiv.innerHTML = `<pre>${results.join('\n')}</pre>`;
        }
        
        async function testAPIEndpoints() {
            const statusDiv = document.getElementById('api-status');
            statusDiv.innerHTML = '<div class="status warning">Testing API endpoints...</div>';
            
            const endpoints = [
                { path: '/browser_info', method: 'GET' },
                { path: '/browser_control_take', method: 'POST', body: { session_id: 'test' } },
                { path: '/browser_control_release', method: 'POST', body: { session_id: 'test' } },
                { path: '/browser_service_control', method: 'POST', body: { action: 'status' } }
            ];
            
            let results = [];
            
            for (const endpoint of endpoints) {
                try {
                    const options = {
                        method: endpoint.method,
                        headers: { 'Content-Type': 'application/json' }
                    };
                    
                    if (endpoint.body) {
                        options.body = JSON.stringify(endpoint.body);
                    }
                    
                    const response = await fetch(endpoint.path, options);
                    results.push(`${endpoint.method} ${endpoint.path}: ${response.ok ? '✅' : '❌'} (${response.status})`);
                } catch (error) {
                    results.push(`${endpoint.method} ${endpoint.path}: ❌ (${error.message})`);
                }
            }
            
            statusDiv.innerHTML = `<pre>${results.join('\n')}</pre>`;
        }
        
        function loadVNCClient() {
            const iframe = document.getElementById('vnc-test-frame');
            iframe.src = 'http://localhost:6080/vnc.html?host=localhost&port=6080';
        }
        
        function loadAgentZeroVNC() {
            const iframe = document.getElementById('vnc-test-frame');
            iframe.src = '/browser_vnc';
        }
        
        // Auto-run initial test
        window.addEventListener('load', () => {
            setTimeout(testBrowserService, 1000);
        });
    </script>
</body>
</html>
