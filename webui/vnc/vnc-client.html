<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Control - Agent Zero</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a1a;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
        }

        .vnc-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .vnc-header {
            background: #2a2a2a;
            color: white;
            padding: 10px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid #444;
            flex-shrink: 0;
        }

        .vnc-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 600;
        }

        .vnc-status {
            padding: 5px 12px;
            background: #333;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
        }

        .vnc-status.connecting {
            background: #f39c12;
            color: #000;
        }

        .vnc-status.connected {
            background: #27ae60;
            color: white;
        }

        .vnc-status.disconnected {
            background: #e74c3c;
            color: white;
        }

        .vnc-status.error {
            background: #c0392b;
            color: white;
        }

        .vnc-screen {
            flex: 1;
            background: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        #vnc-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: #000;
        }

        .vnc-message {
            color: #bbb;
            text-align: center;
            padding: 40px 20px;
            max-width: 500px;
        }

        .vnc-message h2 {
            color: #fff;
            margin-bottom: 15px;
        }

        .vnc-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .vnc-btn {
            background: #444;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: background 0.2s;
        }

        .vnc-btn:hover {
            background: #555;
        }

        .vnc-btn:active {
            background: #666;
        }

        .error-message {
            background: #2c1810;
            border: 1px solid #8b0000;
            border-radius: 6px;
            padding: 20px;
            margin: 20px;
            color: #ff6b6b;
        }

        .loading-spinner {
            border: 3px solid #333;
            border-top: 3px solid #4a90e2;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="vnc-container">
        <div class="vnc-header">
            <div class="vnc-title">
                <span>🖥️ Browser Manual Control</span>
            </div>
            <div class="vnc-controls">
                <button id="reconnect-btn" class="vnc-btn" onclick="reconnectVNC()">Reconnect</button>
                <button id="fullscreen-btn" class="vnc-btn" onclick="toggleFullscreen()">Fullscreen</button>
                <div id="vnc-status" class="vnc-status connecting">Connecting...</div>
            </div>
        </div>
        <div class="vnc-screen" id="vnc-screen">
            <div id="vnc-message" class="vnc-message">
                <div class="loading-spinner"></div>
                <h2>Connecting to Browser</h2>
                <p>Establishing connection to the browser session...</p>
            </div>
        </div>
    </div>

    <script>
        class VNCClient {
            constructor() {
                this.iframe = null;
                this.connected = false;
                this.reconnectAttempts = 0;
                this.maxReconnectAttempts = 5;

                this.init();
            }

            init() {
                this.updateStatus('connecting', 'Connecting...');
                this.loadVNCClient();
            }

            loadVNCClient() {
                try {
                    // Determine the correct noVNC URL
                    const protocol = window.location.protocol;
                    const host = window.location.hostname;
                    const port = '6080'; // websockify port

                    // Try different possible noVNC paths
                    const possiblePaths = [
                        `${protocol}//${host}:${port}/vnc.html`,
                        `${protocol}//${host}:${port}/vnc_lite.html`,
                        `${protocol}//${host}:${port}/`,
                        `${protocol}//${host}:${port}/index.html`
                    ];

                    this.tryLoadVNC(possiblePaths, 0);

                } catch (error) {
                    console.error('Failed to load VNC client:', error);
                    this.updateStatus('error', 'Connection Failed');
                    this.showErrorMessage('Unable to establish connection to the browser service.');
                }
            }

            tryLoadVNC(paths, index) {
                if (index >= paths.length) {
                    this.showErrorMessage('Could not connect to VNC service. Please ensure the browser service is running.');
                    return;
                }

                const currentPath = paths[index];
                console.log(`Trying VNC path: ${currentPath}`);

                // Create iframe for noVNC
                this.iframe = document.createElement('iframe');
                this.iframe.id = 'vnc-iframe';
                this.iframe.src = currentPath;
                this.iframe.style.width = '100%';
                this.iframe.style.height = '100%';
                this.iframe.style.border = 'none';
                this.iframe.style.background = '#000';

                // Set up load handlers
                this.iframe.onload = () => {
                    console.log('VNC client loaded successfully');
                    this.connected = true;
                    this.reconnectAttempts = 0;
                    this.updateStatus('connected', 'Connected');
                    this.showVNCClient();
                };

                this.iframe.onerror = () => {
                    console.log(`Failed to load VNC at ${currentPath}, trying next...`);
                    this.tryLoadVNC(paths, index + 1);
                };

                // Add timeout for loading
                setTimeout(() => {
                    if (!this.connected) {
                        console.log(`Timeout loading VNC at ${currentPath}, trying next...`);
                        this.tryLoadVNC(paths, index + 1);
                    }
                }, 5000);
            }

            updateStatus(status, text) {
                const statusEl = document.getElementById('vnc-status');
                if (statusEl) {
                    statusEl.className = `vnc-status ${status}`;
                    statusEl.textContent = text;
                }
            }

            showVNCClient() {
                const screen = document.getElementById('vnc-screen');
                const message = document.getElementById('vnc-message');

                if (message) {
                    message.style.display = 'none';
                }

                if (this.iframe && screen) {
                    screen.appendChild(this.iframe);
                }
            }

            hideVNCClient() {
                const message = document.getElementById('vnc-message');

                if (this.iframe && this.iframe.parentNode) {
                    this.iframe.parentNode.removeChild(this.iframe);
                }

                if (message) {
                    message.style.display = 'block';
                    message.innerHTML = `
                        <div class="loading-spinner"></div>
                        <h2>Reconnecting...</h2>
                        <p>Attempting to reconnect to browser session...</p>
                    `;
                }
            }

            showErrorMessage(message) {
                const screen = document.getElementById('vnc-screen');
                if (screen) {
                    screen.innerHTML = `
                        <div class="vnc-message">
                            <div class="error-message">
                                <h2>Connection Error</h2>
                                <p>${message}</p>
                                <button class="vnc-btn" onclick="location.reload()">Retry</button>
                            </div>
                        </div>
                    `;
                }
            }

            attemptReconnect() {
                if (this.reconnectAttempts < this.maxReconnectAttempts) {
                    this.reconnectAttempts++;
                    setTimeout(() => {
                        console.log(`Reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
                        this.loadVNCClient();
                    }, 2000 * this.reconnectAttempts);
                } else {
                    this.showErrorMessage('Maximum reconnection attempts reached. Please refresh the page to try again.');
                }
            }

            disconnect() {
                this.connected = false;
                if (this.iframe && this.iframe.parentNode) {
                    this.iframe.parentNode.removeChild(this.iframe);
                    this.iframe = null;
                }
            }
        }

        // Global functions for UI buttons
        let vncClient = null;

        function reconnectVNC() {
            if (vncClient) {
                vncClient.disconnect();
            }
            vncClient = new VNCClient();
        }

        function toggleFullscreen() {
            const container = document.querySelector('.vnc-container');
            if (!document.fullscreenElement) {
                container.requestFullscreen().catch(err => {
                    console.log('Error attempting to enable fullscreen:', err);
                });
            } else {
                document.exitFullscreen();
            }
        }

        // Initialize VNC client when page loads
        document.addEventListener('DOMContentLoaded', () => {
            vncClient = new VNCClient();
        });

        // Handle page unload
        window.addEventListener('beforeunload', () => {
            if (vncClient) {
                vncClient.disconnect();
            }
        });
    </script>
</body>
</html>