"""
Browser service test endpoint - check if browser service is working
"""

from python.helpers.api import <PERSON><PERSON><PERSON><PERSON><PERSON>, Request, Response
from python.helpers.browser_service import get_browser_service
import subprocess
import asyncio


class BrowserTest(ApiHandler):
    """Test browser service functionality"""
    
    @classmethod
    def requires_auth(cls) -> bool:
        return True
    
    @classmethod
    def requires_csrf(cls) -> bool:
        return True
    
    @classmethod
    def get_methods(cls) -> list[str]:
        return ["GET", "POST"]
    
    async def process(self, input: dict, request: Request) -> dict | Response:
        """
        Test browser service components and connectivity
        """
        try:
            test_results = {}
            
            # Test 1: Check browser service instance
            try:
                browser_service = await get_browser_service()
                test_results["browser_service_instance"] = {
                    "status": "success",
                    "is_running": browser_service.is_running,
                    "is_healthy": browser_service.is_healthy()
                }
                
                if browser_service.is_running:
                    connection_info = browser_service.get_connection_info()
                    test_results["connection_info"] = connection_info
                else:
                    test_results["connection_info"] = None
                    
            except Exception as e:
                test_results["browser_service_instance"] = {
                    "status": "error",
                    "error": str(e)
                }
            
            # Test 2: Check individual processes
            processes_status = {}
            
            # Check Xvfb
            try:
                result = subprocess.run(['pgrep', 'Xvfb'], capture_output=True)
                processes_status['xvfb'] = {
                    "running": result.returncode == 0,
                    "pid": result.stdout.decode().strip() if result.returncode == 0 else None
                }
            except Exception as e:
                processes_status['xvfb'] = {"running": False, "error": str(e)}
            
            # Check x11vnc
            try:
                result = subprocess.run(['pgrep', 'x11vnc'], capture_output=True)
                processes_status['vnc'] = {
                    "running": result.returncode == 0,
                    "pid": result.stdout.decode().strip() if result.returncode == 0 else None
                }
            except Exception as e:
                processes_status['vnc'] = {"running": False, "error": str(e)}
            
            # Check websockify
            try:
                result = subprocess.run(['pgrep', '-f', 'websockify'], capture_output=True)
                processes_status['websockify'] = {
                    "running": result.returncode == 0,
                    "pid": result.stdout.decode().strip() if result.returncode == 0 else None
                }
            except Exception as e:
                processes_status['websockify'] = {"running": False, "error": str(e)}
            
            # Check Chrome/Chromium
            try:
                result = subprocess.run(['pgrep', '-f', 'chrome|chromium'], capture_output=True)
                processes_status['browser'] = {
                    "running": result.returncode == 0,
                    "pid": result.stdout.decode().strip() if result.returncode == 0 else None
                }
            except Exception as e:
                processes_status['browser'] = {"running": False, "error": str(e)}
            
            test_results["processes"] = processes_status
            
            # Test 3: Check port connectivity
            ports_status = {}
            for port_name, port in [("vnc", 5900), ("novnc", 6080), ("cdp", 9222)]:
                try:
                    result = subprocess.run(['netstat', '-ln'], capture_output=True, text=True)
                    port_listening = f":{port}" in result.stdout
                    ports_status[port_name] = {
                        "port": port,
                        "listening": port_listening
                    }
                except Exception as e:
                    ports_status[port_name] = {
                        "port": port,
                        "listening": False,
                        "error": str(e)
                    }
            
            test_results["ports"] = ports_status
            
            # Overall status
            all_processes_running = all(p.get("running", False) for p in processes_status.values())
            all_ports_listening = all(p.get("listening", False) for p in ports_status.values())
            
            overall_status = "healthy" if all_processes_running and all_ports_listening else "unhealthy"
            
            return {
                "status": "success",
                "overall_status": overall_status,
                "timestamp": asyncio.get_event_loop().time(),
                "tests": test_results,
                "summary": {
                    "all_processes_running": all_processes_running,
                    "all_ports_listening": all_ports_listening,
                    "browser_service_healthy": test_results.get("browser_service_instance", {}).get("is_healthy", False)
                }
            }
                
        except Exception as e:
            return {
                "status": "error",
                "message": f"Browser service test failed: {str(e)}"
            }
