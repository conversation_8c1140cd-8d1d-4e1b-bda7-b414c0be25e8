"""
Browser Info API - Get current browser session info
"""

from python.helpers.api import <PERSON><PERSON><PERSON><PERSON><PERSON>, Request, Response
import agent as agent_helper


class BrowserInfo(ApiHandler):
    """Get current browser session information"""
    
    @classmethod
    def requires_auth(cls) -> bool:
        return True
    
    @classmethod
    def requires_csrf(cls) -> bool:
        return True
    
    @classmethod
    def get_methods(cls) -> list[str]:
        return ["GET"]
    
    async def process(self, input: dict, request: Request) -> dict | Response:
        """
        Get browser info from the current agent
        """
        try:
            # Get the current agent
            agents = agent_helper.get_agents()
            
            if not agents:
                return {
                    "status": "error",
                    "message": "No active agents found"
                }
            
            # Use the first agent (or we could get by context ID)
            agent = agents[0]
            
            if not hasattr(agent, 'data') or not agent.data:
                return {
                    "status": "error", 
                    "message": "No browser data available"
                }
            
            browser_info = {}
            
            # Get VNC URL if available
            if 'browser_vnc_url' in agent.data:
                browser_info['vnc_url'] = agent.data['browser_vnc_url']
            
            # Get interactive availability
            if 'browser_interactive_available' in agent.data:
                browser_info['interactive_available'] = agent.data['browser_interactive_available']
            
            # Get session info
            browser_info['session_id'] = agent.context.id
            browser_info['agent_name'] = getattr(agent.context, 'name', f'Agent {agent.context.no}')
            
            if not browser_info.get('vnc_url'):
                return {
                    "status": "error",
                    "message": "No VNC URL available. Browser may be running in headless mode."
                }
            
            return {
                "status": "success",
                "browser_info": browser_info
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"Failed to get browser info: {str(e)}"
            }
