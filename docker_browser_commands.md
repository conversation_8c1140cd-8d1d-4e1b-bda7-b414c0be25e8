# Docker Browser Service Commands

## Quick Fix for Docker Browser Service

If your Agent Zero is running in Docker and the browser service isn't working, here are the commands to fix it:

### 1. Copy the fix script to your Docker container

```bash
# Find your Agent Zero container name/ID
docker ps | grep agent

# Copy the fix script to the container (replace CONTAINER_NAME with your actual container name)
docker cp fix_docker_browser.sh CONTAINER_NAME:/tmp/fix_docker_browser.sh
docker cp debug_docker_browser.sh CONTAINER_NAME:/tmp/debug_docker_browser.sh
```

### 2. Run the debug script to see what's wrong

```bash
# Execute the debug script inside the container
docker exec -it CONTAINER_NAME bash /tmp/debug_docker_browser.sh
```

### 3. Run the fix script to install and start browser service

```bash
# Execute the fix script inside the container
docker exec -it CONTAINER_NAME bash /tmp/fix_docker_browser.sh
```

### 4. Verify the services are running

```bash
# Check if all processes are running
docker exec -it CONTAINER_NAME bash -c "
echo 'Process Status:'
pgrep -x Xvfb > /dev/null && echo '✅ Xvfb running' || echo '❌ Xvfb not running'
pgrep -x x11vnc > /dev/null && echo '✅ VNC running' || echo '❌ VNC not running'
pgrep -f websockify > /dev/null && echo '✅ websockify running' || echo '❌ websockify not running'
pgrep -f chrome > /dev/null && echo '✅ Chrome running' || echo '❌ Chrome not running'
echo
echo 'Port Status:'
netstat -ln | grep ':5900 ' && echo '✅ VNC port 5900 listening' || echo '❌ VNC port 5900 not listening'
netstat -ln | grep ':6080 ' && echo '✅ noVNC port 6080 listening' || echo '❌ noVNC port 6080 not listening'
netstat -ln | grep ':9222 ' && echo '✅ CDP port 9222 listening' || echo '❌ CDP port 9222 not listening'
"
```

## Docker Run Command with Proper Port Mapping

If you're starting a new Agent Zero container, make sure to map the browser service ports:

```bash
docker run -d \
  --name agent-zero \
  -p 50080:80 \
  -p 56080:6080 \
  -p 55900:5900 \
  -p 59222:9222 \
  -v $(pwd):/a0 \
  agent0ai/agent-zero:latest
```

## Test URLs After Fix

Once the fix is applied, test these URLs:

1. **Agent Zero VNC Client**: http://localhost:50080/browser_vnc
2. **Direct noVNC**: http://localhost:56080/vnc.html
3. **Chrome DevTools**: http://localhost:59222
4. **Browser Service Test**: http://localhost:50080/test-browser-service.html

## Manual Browser Service Start (Alternative)

If the fix script doesn't work, you can manually start the services:

```bash
# Enter the container
docker exec -it CONTAINER_NAME bash

# Set environment
export DISPLAY=:99
export HOME=/tmp
export USER=root

# Start services manually
Xvfb :99 -screen 0 1920x1080x24 -ac +extension GLX +render -noreset &
sleep 2
fluxbox -display :99 &
sleep 1
x11vnc -display :99 -nopw -forever -shared -rfbport 5900 -bg
sleep 1
websockify --web /usr/share/novnc 6080 localhost:5900 &
sleep 2
chromium --remote-debugging-port=9222 --remote-debugging-address=0.0.0.0 --user-data-dir=/tmp/browser_profile_manual --no-first-run --no-default-browser-check --disable-default-apps --disable-dev-shm-usage --disable-gpu --no-sandbox --disable-setuid-sandbox --window-size=1920,1080 --window-position=0,0 about:blank &
```

## Enable Interactive Mode in Settings

After the browser service is running:

1. Go to Agent Zero web interface: http://localhost:50080
2. Click Settings (gear icon)
3. Go to "Browser Model" section
4. Enable "Enable Interactive Mode"
5. Save settings

## Troubleshooting

### If VNC connection fails:
- Check if port 6080 is accessible: `curl http://localhost:56080/vnc.html`
- Check websockify logs: `docker exec CONTAINER_NAME ps aux | grep websockify`

### If Chrome doesn't start:
- Check if chromium is installed: `docker exec CONTAINER_NAME which chromium`
- Check Chrome process: `docker exec CONTAINER_NAME ps aux | grep chrome`

### If Agent Zero can't connect to browser:
- Check CDP port: `curl http://localhost:59222/json`
- Verify browser agent settings in Agent Zero

## Expected Behavior After Fix

1. **Browser Agent**: Connects to Chrome via CDP (port 9222)
2. **User VNC Access**: Connects to same Chrome via VNC (port 6080)
3. **Shared Session**: Both agent and user see the same browser windows
4. **Manual Control**: User can take control when needed (captchas, complex interactions)

The key is that both the agent (via CDP) and user (via VNC) are controlling the **same Chrome browser instance**.
